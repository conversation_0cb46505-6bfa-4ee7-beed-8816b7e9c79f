<!DOCTYPE html>
<html lang="nl" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Winkelmandje - <PERSON>ita's Baby Shop</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="{{ url_for('index') }}">
                <i class="bi bi-shop"></i> Tita's Baby Shop
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="bi bi-grid"></i> Collectie
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('basket') }}">
                            <i class="bi bi-basket"></i> Winkelmandje
                            <span class="badge bg-danger ms-1" id="basket-count">0</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin') }}">
                            <i class="bi bi-shield-lock"></i> Admin
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <div class="bg-gradient py-4">
        <div class="container">
            <h1 class="text-white mb-0">
                <i class="bi bi-basket"></i> Winkelmandje
            </h1>
        </div>
    </div>

    <!-- Basket Content -->
    <div class="container my-4">
        <!-- Empty Basket Message -->
        <div id="empty-basket" class="text-center py-5" style="display: none;">
            <i class="bi bi-basket display-1 text-muted"></i>
            <h3 class="mt-3">Je winkelmandje is leeg</h3>
            <p class="text-muted">Voeg items toe vanuit onze collectie om ze hier te zien.</p>
            <a href="{{ url_for('index') }}" class="btn btn-primary">
                <i class="bi bi-arrow-left"></i> Terug naar Collectie
            </a>
        </div>

        <!-- Basket Items -->
        <div id="basket-content" style="display: none;">
            <!-- Basket Actions -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h4>
                    <i class="bi bi-list"></i> Geselecteerde Items
                    <span class="badge bg-primary" id="item-count">0</span>
                </h4>
                <div>
                    <button class="btn btn-outline-danger" onclick="clearBasket()">
                        <i class="bi bi-trash"></i> Leeg Winkelmandje
                    </button>
                    <a href="{{ url_for('index') }}" class="btn btn-outline-primary">
                        <i class="bi bi-plus"></i> Meer Items Toevoegen
                    </a>
                </div>
            </div>

            <!-- Items Table -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Foto</th>
                                    <th>Item ID</th>
                                    <th>Geslacht</th>
                                    <th>Maat</th>
                                    <th>Prijs</th>
                                    <th>Brand</th>
                                    <th>Category</th>
                                    <th>Acties</th>
                                </tr>
                            </thead>
                            <tbody id="basket-items">
                                <!-- Items will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Total -->
            <div class="card mt-3">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-0">
                                <i class="bi bi-calculator"></i> Totaal
                            </h5>
                            <small class="text-muted">
                                Neem contact met ons op om deze items te reserveren
                            </small>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <h3 class="text-success mb-0">
                                €<span id="basket-total">0.00</span>
                            </h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Info -->
            <div class="alert alert-info mt-3">
                <h6><i class="bi bi-info-circle"></i> Volgende Stappen</h6>
                <p class="mb-0">
                    Deel de Item ID's uit je winkelmandje met ons om deze items te reserveren. 
                    We nemen dan contact met je op voor verdere afspraken.
                </p>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="bi bi-shop"></i> Tita's Baby Shop</h5>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted mb-0">&copy; 2025 Tita's Baby Shop</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Basket JS -->
    <script src="{{ url_for('static', filename='basket.js') }}"></script>
    
    <style>
        .bg-gradient {
            background: linear-gradient(135deg, var(--bs-primary) 0%, #8b5cf6 100%);
        }
        
        .navbar-brand {
            font-size: 1.5rem;
        }
        
        .sticky-top {
            backdrop-filter: blur(10px);
        }
    </style>
</body>
</html>
