{% extends "admin_base.html" %}

{% block title %}Nieuw Item - Tita's Baby Shop{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h3 class="card-title mb-0">
                    <i class="bi bi-plus-circle"></i> Nieuw Item Toevoegen
                </h3>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="itemForm">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <!-- Geslacht -->
                        <div class="col-md-6 mb-3">
                            {{ form.geslacht.label(class="form-label") }}
                            {{ form.geslacht(class="form-select") }}
                            {% if form.geslacht.errors %}
                                <div class="text-danger">
                                    {% for error in form.geslacht.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Maat -->
                        <div class="col-md-6 mb-3">
                            {{ form.maat.label(class="form-label") }}
                            {{ form.maat(class="form-select") }}
                            {% if form.maat.errors %}
                                <div class="text-danger">
                                    {% for error in form.maat.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- Prijs -->
                        <div class="col-md-6 mb-3">
                            {{ form.prijs.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">€</span>
                                {{ form.prijs(class="form-control", step="0.01", placeholder="0.00") }}
                            </div>
                            {% if form.prijs.errors %}
                                <div class="text-danger">
                                    {% for error in form.prijs.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Brand -->
                        <div class="col-md-6 mb-3">
                            {{ form.brand.label(class="form-label") }}
                            {{ form.brand(class="form-select") }}
                            {% if form.brand.errors %}
                                <div class="text-danger">
                                    {% for error in form.brand.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Category -->
                    <div class="mb-3">
                        {{ form.category.label(class="form-label") }}
                        {{ form.category(class="form-select") }}
                        {% if form.category.errors %}
                            <div class="text-danger">
                                {% for error in form.category.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Photo Upload -->
                    <div class="mb-4">
                        <label class="form-label">Foto</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-2">
                                    {{ form.foto(class="form-control", accept="image/*") }}
                                    {% if form.foto.errors %}
                                        <div class="text-danger">
                                            {% for error in form.foto.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">

                            </div>
                        </div>
                        
                        <!-- Camera Preview -->
                        <div id="cameraSection" class="mt-3" style="display: none;">
                            <video id="video" class="img-fluid rounded mb-2" autoplay style="max-height: 300px;"></video>
                            <canvas id="canvas" style="display: none;"></canvas>
                            <div>
                                <button type="button" class="btn btn-success me-2" id="captureBtn">
                                    <i class="bi bi-camera-fill"></i> Foto Maken
                                </button>
                                <button type="button" class="btn btn-secondary" id="stopCameraBtn">
                                    <i class="bi bi-x-circle"></i> Camera Stoppen
                                </button>
                            </div>
                        </div>
                        
                        <!-- Image Preview -->
                        <div id="imagePreview" class="mt-3" style="display: none;">
                            <img id="previewImg" class="img-fluid rounded" style="max-height: 300px;">
                            <div class="mt-2">
                                <button type="button" class="btn btn-danger btn-sm" id="removeImageBtn">
                                    <i class="bi bi-trash"></i> Foto Verwijderen
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary btn-lg") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Camera functionality will be added in app.js
</script>
{% endblock %}
